{"_type": "export", "__export_format": 4, "__export_date": "2024-01-19T12:00:00.000Z", "__export_source": "insomnia.desktop.app:v2023.5.8", "resources": [{"_id": "wrk_lavalink_rust", "parentId": null, "modified": 1705665600000, "created": 1705665600000, "name": "Lavalink Rust", "description": "API collection for testing Lavalink Rust implementation", "scope": "collection", "_type": "workspace"}, {"_id": "env_lavalink_rust", "parentId": "wrk_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "name": "Lavalink Rust Environment", "description": "Environment variables for Lavalink Rust testing", "environment": {"URL": "http://localhost:2333", "WS": "ws://localhost:2333", "VERSION": "/v4", "SESSION_ID": "rust-session-example", "USER_ID": "817403182526365706", "GUILD_ID": "817327181659111454", "AUTHORIZATION": "you<PERSON>ll<PERSON><PERSON>", "CLIENT_NAME": "lavalink-rust-test/1.0.0"}, "environmentPropertyOrder": {"&": ["URL", "WS", "VERSION", "SESSION_ID", "USER_ID", "GUILD_ID", "AUTHORIZATION", "CLIENT_NAME"]}, "metaSortKey": 1705665600000, "_type": "request_group"}, {"_id": "ws_websocket", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "name": "WebSocket Connection", "url": "{{WS}}{{VERSION}}/websocket", "metaSortKey": -1705665600000, "headers": [{"id": "header_user_id", "name": "User-Id", "value": "{{USER_ID}}", "description": "Discord bot user ID"}, {"id": "header_client_name", "name": "Client-Name", "value": "{{CLIENT_NAME}}", "description": "Client name and version"}], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "parameters": [], "settingEncodeUrl": true, "settingStoreCookies": true, "settingSendCookies": true, "settingFollowRedirects": "global", "description": "WebSocket connection to Lavalink Rust server", "_type": "websocket_request"}, {"_id": "req_version", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}/version", "name": "GET /version", "description": "Get Lavalink Rust version", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665599000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_info", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/info", "name": "GET /v4/info", "description": "Get Lavalink Rust server information", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665598000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_stats", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/stats", "name": "GET /v4/stats", "description": "Get Lavalink Rust server statistics", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665597000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_loadtracks_youtube", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/loadtracks", "name": "GET /v4/loadtracks (YouTube)", "description": "Load tracks from YouTube", "method": "GET", "body": {}, "parameters": [{"id": "param_identifier", "name": "identifier", "value": "ytsearch:<PERSON> Gonna Give You Up", "description": "YouTube search query"}], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665596000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_loadtracks_spotify_fallback", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/loadtracks", "name": "GET /v4/loadtracks (Spotify Fallback)", "description": "Load tracks using Spotify URL fallback system", "method": "GET", "body": {}, "parameters": [{"id": "param_spotify_url", "name": "identifier", "value": "https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh", "description": "Spotify track URL (will be converted to YouTube search)"}], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665595000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_decodetrack", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/decodetrack", "name": "GET /v4/decodetrack", "description": "Decode a single track", "method": "GET", "body": {}, "parameters": [{"id": "param_encoded_track", "name": "encodedTrack", "value": "QAAAjQIAJVJpY2sgQXN0bGV5IC0gTmV2ZXIgR29ubmEgR2l2ZSBZb3UgVXAADlJpY2tBc3RsZXlWRVZPAAAAAAADPCAAC2RRdzR3OVdnWGNRAAEAK2h0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9ZFF3NHc5V2dYY1EAB3lvdXR1YmUAAAAAAAAAAA==", "description": "Base64 encoded track data"}], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665594000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_player_update", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/sessions/{{SESSION_ID}}/players/{{GUILD_ID}}", "name": "PATCH /v4/sessions/{sessionId}/players/{guildId}", "description": "Update or create player with track and filters", "method": "PATCH", "body": {"mimeType": "application/json", "text": "{\n  \"track\": {\n    \"encoded\": \"QAAAjQIAJVJpY2sgQXN0bGV5IC0gTmV2ZXIgR29ubmEgR2l2ZSBZb3UgVXAADlJpY2tBc3RsZXlWRVZPAAAAAAADPCAAC2RRdzR3OVdnWGNRAAEAK2h0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9ZFF3NHc5V2dYY1EAB3lvdXR1YmUAAAAAAAAAAA==\",\n    \"userData\": {}\n  },\n  \"position\": 0,\n  \"volume\": 100,\n  \"paused\": false,\n  \"filters\": {\n    \"volume\": 1.0,\n    \"equalizer\": [\n      {\n        \"band\": 0,\n        \"gain\": 0.2\n      }\n    ],\n    \"karaoke\": {\n      \"level\": 1.0,\n      \"monoLevel\": 1.0,\n      \"filterBand\": 220.0,\n      \"filterWidth\": 100.0\n    },\n    \"timescale\": {\n      \"speed\": 1.0,\n      \"pitch\": 1.0,\n      \"rate\": 1.0\n    },\n    \"tremolo\": {\n      \"frequency\": 2.0,\n      \"depth\": 0.5\n    },\n    \"vibrato\": {\n      \"frequency\": 2.0,\n      \"depth\": 0.5\n    },\n    \"rotation\": {\n      \"rotationHz\": 0.2\n    },\n    \"distortion\": {\n      \"sinOffset\": 0.0,\n      \"sinScale\": 1.0,\n      \"cosOffset\": 0.0,\n      \"cosScale\": 1.0,\n      \"tanOffset\": 0.0,\n      \"tanScale\": 1.0,\n      \"offset\": 0.0,\n      \"scale\": 1.0\n    },\n    \"channelMix\": {\n      \"leftToLeft\": 1.0,\n      \"leftToRight\": 0.0,\n      \"rightToLeft\": 0.0,\n      \"rightToRight\": 1.0\n    },\n    \"lowPass\": {\n      \"smoothing\": 20.0\n    }\n  },\n  \"voice\": {\n    \"token\": \"discord-voice-token\",\n    \"endpoint\": \"discord-voice-endpoint\",\n    \"sessionId\": \"discord-voice-session-id\"\n  }\n}"}, "parameters": [{"id": "param_no_replace", "name": "noReplace", "value": "false", "description": "Whether to replace current track"}], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665593000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_player_delete", "parentId": "env_lavalink_rust", "modified": 1705665600000, "created": 1705665600000, "url": "{{URL}}{{VERSION}}/sessions/{{SESSION_ID}}/players/{{GUILD_ID}}", "name": "DELETE /v4/sessions/{sessionId}/players/{guildId}", "description": "Destroy player for guild", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "apikey", "disabled": false, "key": "Authorization", "value": "{{AUTHORIZATION}}", "addTo": "header"}, "metaSortKey": -1705665592000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}]}