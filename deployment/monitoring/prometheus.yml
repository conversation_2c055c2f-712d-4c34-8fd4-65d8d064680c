global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "lavalink_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Lavalink Rust metrics
  - job_name: 'lavalink-rust'
    static_configs:
      - targets: ['lavalink-rust:9090']
    scrape_interval: 10s
    metrics_path: /metrics
    scheme: http

  # System metrics (if node_exporter is available)
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
