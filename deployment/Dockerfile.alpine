# Runtime stage for pre-built binary (Alpine variant)
FROM alpine:3.18

# Install runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    openssl \
    opus \
    ffmpeg \
    python3 \
    py3-pip \
    curl

# Install yt-dlp for audio source extraction
# Note: Alpine doesn't have PEP 668 restrictions, but keeping consistent
RUN pip3 install --no-cache-dir yt-dlp

# Create non-root user
RUN addgroup -g 322 lavalink && \
    adduser -D -u 322 -G lavalink lavalink

# Set working directory
WORKDIR /app

# Copy pre-built binary from CI
COPY lavalink-rust-musl /app/lavalink-rust

# Create necessary directories
RUN mkdir -p /app/logs /app/plugins && \
    chown -R lavalink:lavalink /app

# Switch to non-root user
USER lavalink

# Expose ports
EXPOSE 2333 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:2333/v4/info || exit 1

# Default command
ENTRYPOINT ["/app/lavalink-rust"]
CMD ["--config", "/app/application.yml"]
