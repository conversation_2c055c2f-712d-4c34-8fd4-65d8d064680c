name: Documentation

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'docs/**'
      - '.github/workflows/docs.yml'
      - 'mkdocs.yml'
  pull_request:
    paths:
      - 'docs/**'
      - 'mkdocs.yml'

concurrency:
  group: docs-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: pip-docs-${{ hashFiles('docs/requirements.txt') }}
          restore-keys: |
            pip-docs-

      - name: Install documentation dependencies
        run: |
          pip install -r docs/requirements.txt

      - name: Build documentation
        run: |
          mkdocs build --verbose

      - name: Upload documentation artifact
        uses: actions/upload-artifact@v4
        with:
          name: documentation
          path: site/

  deploy-docs:
    needs: build-docs
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      contents: read
      deployments: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download documentation artifact
        uses: actions/download-artifact@v4
        with:
          name: documentation
          path: site/

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: ${{ vars.CLOUDFLARE_PROJECT_NAME }}
          directory: site
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          wranglerVersion: '3'
