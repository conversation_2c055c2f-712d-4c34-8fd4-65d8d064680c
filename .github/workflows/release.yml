name: Release

on:
  release:
    types: [ published ]

jobs:
  build:
    uses: ./.github/workflows/build.yml
    secrets:
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_TOKEN: ${{ secrets.DOCKER_TOKEN }}
      DOCKER_REGISTRY: ${{ secrets.DOCKER_REGISTRY }}
      DOCKER_IMAGE: ${{ secrets.DOCKER_IMAGE }}
      CRATES_IO_TOKEN: ${{ secrets.CRATES_IO_TOKEN }}

  release:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download Linux x64 binary
        uses: actions/download-artifact@v4
        with:
          name: lavalink-rust-linux-x64

      - name: Download Linux x64 musl binary
        uses: actions/download-artifact@v4
        with:
          name: lavalink-rust-linux-x64-musl

      - name: Download Linux ARM64 binary
        uses: actions/download-artifact@v4
        with:
          name: lavalink-rust-linux-arm64

      - name: Create release archives
        run: |
          # Create archives for each binary
          tar -czf lavalink-rust-linux-x64.tar.gz lavalink-rust
          tar -czf lavalink-rust-linux-x64-musl.tar.gz lavalink-rust-musl
          tar -czf lavalink-rust-linux-arm64.tar.gz lavalink-rust-arm64
          
          # Create checksums
          sha256sum *.tar.gz > checksums.txt

      - name: Upload Release Assets
        uses: ncipollo/release-action@v1
        with:
          artifacts: |
            lavalink-rust-linux-x64.tar.gz
            lavalink-rust-linux-x64-musl.tar.gz
            lavalink-rust-linux-arm64.tar.gz
            checksums.txt
          allowUpdates: true
          omitBodyDuringUpdate: true
          omitDraftDuringUpdate: true
          omitNameDuringUpdate: true
          omitPrereleaseDuringUpdate: true
