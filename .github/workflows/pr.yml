name: Pull Request

on:
  pull_request:
    branches: [ main, dev ]
    paths-ignore:
      - '**.md'
      - 'docs/**'

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            libssl-dev \
            libopus-dev \
            libavcodec-dev \
            libavformat-dev \
            libavutil-dev \
            libavfilter-dev \
            libavdevice-dev

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true

      - name: Check formatting
        run: cargo fmt --all -- --check

      - name: Run Clippy
        run: cargo clippy --all-targets --all-features -- -D warnings

      - name: Run tests
        run: cargo test --all-features --workspace

      - name: Run integration tests
        run: cargo test --test integration_tests

  build-check:
    name: Build Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            libssl-dev \
            libopus-dev \
            libavcodec-dev \
            libavformat-dev \
            libavutil-dev \
            libavfilter-dev \
            libavdevice-dev

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true

      - name: Build project
        run: cargo build --release

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Run security audit
        run: cargo audit

  dependency-check:
    name: Dependency Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Install cargo-outdated
        run: cargo install cargo-outdated

      - name: Check for outdated dependencies
        run: |
          echo "Checking for outdated dependencies (informational only):"
          cargo outdated || echo "Some dependencies are outdated, but this is informational only"
