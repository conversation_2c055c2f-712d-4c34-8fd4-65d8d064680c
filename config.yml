server:
  port: 2333
  address: "0.0.0.0"
  password: "youshallnotpass"

lavalink:
  server:
    password: "youshallnotpass"
    sources:
      youtube: true
      bandcamp: true
      soundcloud: true
      twitch: true
      vimeo: true
      http: true
      local: false
    filters:
      volume: true
      equalizer: true
      karaoke: true
      timescale: true
      tremolo: true
      vibrato: true
      distortion: true
      rotation: true
      channelMix: true
      lowPass: true
    bufferDurationMs: 400
    frameBufferDurationMs: 5000
    opusEncodingQuality: 10
    resamplingQuality: "LOW"
    trackStuckThresholdMs: 10000
    useSeekGhosting: true
    youtubePlaylistLoadLimit: 6
    playerUpdateInterval: 5
    youtubeSearchEnabled: true
    soundcloudSearchEnabled: true
    gc-warnings: true

metrics:
  prometheus:
    enabled: false
    endpoint: "/metrics"

sentry:
  dsn: ""
  environment: ""

logging:
  file:
    path: "./logs/"
  level:
    root: "INFO"
    lavalink: "INFO"

  logback:
    rollingpolicy:
      max-file-size: "25MB"
      max-history: 30
